<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\controller\Frontend;
use app\common\service\PerformanceMonitor;
use app\common\service\BulletPriceService;
use think\facade\Log;

/**
 * 子弹价格控制器 - Service架构版本
 * 
 * 重构记录:
 * 2025-08-03: Service架构重构
 *   - 业务逻辑迁移到BulletPriceService
 *   - 控制器只负责参数处理和响应
 *   - 统一缓存管理通过Service层
 */
class BulletPrice extends Frontend
{
    /**
     * 无需登录的方法
     * @var array
     */
    protected array $noNeedLogin = ['getBulletTypes', 'getPredictList', 'updatePredict', 'getBulletPriceTrend'];

    private BulletPriceService $bulletPriceService;

    public function initialize(): void
    {
        parent::initialize();
        $this->bulletPriceService = new BulletPriceService();
    }

    /**
     * 获取子弹类型列表
     */
    public function getBulletTypes(): void
    {
        $monitor = PerformanceMonitor::startApi('getBulletTypes', request()->param());

        try {
            $data = $this->bulletPriceService->getBulletTypes();

            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取子弹类型失败: ' . $e->getMessage());
            $this->error('获取子弹类型失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取价格预测列表
     */
    public function getPredictList(): void
    {
        $monitor = PerformanceMonitor::startApi('getPredictList', request()->param());

        try {
            $params = [
                'bullet_type' => request()->param('bullet_type', ''),
                'limit' => min(100, max(1, intval(request()->param('limit', 20))))
            ];

            $data = $this->bulletPriceService->getPredictList($params);

            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取价格预测失败: ' . $e->getMessage());
            $this->error('获取价格预测失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新价格预测
     */
    public function updatePredict(): void
    {
        $monitor = PerformanceMonitor::startApi('updatePredict', request()->param());

        try {
            $objectId = intval(request()->param('object_id', 0));

            if ($objectId <= 0) {
                $this->error('物品ID无效');
                return;
            }

            $predictData = [
                'predicted_price' => floatval(request()->param('predicted_price', 0)),
                'confidence_score' => floatval(request()->param('confidence_score', 0)),
                'trend_direction' => request()->param('trend_direction', 'stable'),
                'factors' => request()->param('factors', [])
            ];

            // 验证数据
            if ($predictData['predicted_price'] <= 0) {
                $this->error('预测价格必须大于0');
                return;
            }

            if ($predictData['confidence_score'] < 0 || $predictData['confidence_score'] > 1) {
                $this->error('置信度分数必须在0-1之间');
                return;
            }

            $allowedDirections = ['up', 'down', 'stable'];
            if (!in_array($predictData['trend_direction'], $allowedDirections)) {
                $predictData['trend_direction'] = 'stable';
            }

            $result = $this->bulletPriceService->updatePredict($objectId, $predictData);

            PerformanceMonitor::endApi($monitor, ['success' => $result], false);

            if ($result) {
                $this->success('更新成功', ['success' => true]);
            } else {
                $this->error('更新失败');
            }

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('更新价格预测失败: ' . $e->getMessage());
            $this->error('更新价格预测失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取子弹价格趋势分析
     */
    public function getBulletPriceTrend(): void
    {
        $monitor = PerformanceMonitor::startApi('getBulletPriceTrend', request()->param());

        try {
            $bulletType = request()->param('bullet_type', '');
            $days = min(90, max(1, intval(request()->param('days', 30))));

            $data = $this->bulletPriceService->getBulletPriceTrend($bulletType, $days);

            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取价格趋势失败: ' . $e->getMessage());
            $this->error('获取价格趋势失败: ' . $e->getMessage());
        }
    }
}