<?php

namespace app\api\controller;

use app\common\controller\Frontend;
use app\common\service\PerformanceMonitor;
use app\common\service\ItemsService;
use think\facade\Log;
use think\App;

/**
 * 物品管理控制器 - Service架构版本
 * 
 * 重构记录:
 * 2025-08-03: Service架构重构
 *   - 业务逻辑迁移到ItemsService
 *   - 控制器只负责参数处理和响应
 *   - 统一缓存管理通过Service层
 */
class Items extends Frontend
{
    protected array $noNeedLogin = ['getItemsList', 'getDetail', 'getPriceHistory', 'getCategories', 'getBatchDetails', 'getBatchPrices'];
    protected array $noNeedPermission = [];

    private ItemsService $itemsService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->itemsService = new ItemsService();
    }

    /**
     * 获取物品列表
     */
    public function getItemsList(): void
    {
        $monitor = PerformanceMonitor::startApi('getItemsList', request()->param());

        try {
            // 获取并验证参数
            $params = [
                'page' => max(1, intval(request()->param('page', 1))),
                'pageSize' => min(100, max(1, intval(request()->param('pageSize', 20)))),
                'category_id' => request()->param('category_id', ''),
                'search' => trim(request()->param('search', '')),
                'grade' => request()->param('grade', ''),
                'sort' => request()->param('sort', ''),
                'sort_field' => request()->param('sort_field', 'object_id'),
                'sort_order' => request()->param('sort_order', 'asc')
            ];

            $data = $this->itemsService->getItemsList($params);

            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取物品列表失败: ' . $e->getMessage());
            $this->error('获取物品列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取物品详情
     */
    public function getDetail(): void
    {
        $monitor = PerformanceMonitor::startApi('getDetail', request()->param());

        try {
            $itemId = intval(request()->param('object_id', 0));

            if ($itemId <= 0) {
                $this->error('物品ID无效');
                return;
            }

            $data = $this->itemsService->getItemDetail($itemId);

            if (!$data) {
                $this->error('物品不存在');
                return;
            }

            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取物品详情失败: ' . $e->getMessage());
            $this->error('获取物品详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取物品价格历史
     */
    public function getPriceHistory(): void
    {
        $monitor = PerformanceMonitor::startApi('getPriceHistory', request()->param());

        try {
            $itemId = intval(request()->param('object_id', 0));
            $timeRange = request()->param('time_range', '30d');

            if ($itemId <= 0) {
                $this->error('物品ID无效');
                return;
            }

            // 验证时间范围
            $allowedRanges = ['7d', '30d', '90d'];
            if (!in_array($timeRange, $allowedRanges)) {
                $timeRange = '30d';
            }

            $data = $this->itemsService->getItemPriceHistory($itemId, $timeRange);

            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取价格历史失败: ' . $e->getMessage());
            $this->error('获取价格历史失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取分类列表
     */
    public function getCategories(): void
    {
        $monitor = PerformanceMonitor::startApi('getCategories', request()->param());

        try {
            $data = $this->itemsService->getCategories();

            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取分类列表失败: ' . $e->getMessage());
            $this->error('获取分类列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量获取物品详情
     */
    public function getBatchDetails(): void
    {
        $monitor = PerformanceMonitor::startApi('getBatchDetails', request()->param());

        try {
            $itemIds = request()->param('ids', '');

            // 解析物品ID列表
            if (is_string($itemIds)) {
                $itemIds = array_filter(explode(',', $itemIds));
            } elseif (!is_array($itemIds)) {
                $itemIds = [];
            }

            $itemIds = array_map('intval', $itemIds);
            $itemIds = array_filter($itemIds, function($id) {
                return $id > 0;
            });

            if (empty($itemIds)) {
                $this->error('物品ID列表为空');
                return;
            }

            // 限制批量查询数量
            if (count($itemIds) > 50) {
                $this->error('批量查询数量不能超过50个');
                return;
            }

            $data = $this->itemsService->getBatchItemDetails($itemIds);

            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('批量获取物品详情失败: ' . $e->getMessage());
            $this->error('批量获取物品详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量获取物品价格
     */
    public function getBatchPrices(): void
    {
        $monitor = PerformanceMonitor::startApi('getBatchPrices', request()->param());

        try {
            $itemIds = request()->param('ids', '');

            // 解析物品ID列表
            if (is_string($itemIds)) {
                $itemIds = array_filter(explode(',', $itemIds));
            } elseif (!is_array($itemIds)) {
                $itemIds = [];
            }

            $itemIds = array_map('intval', $itemIds);
            $itemIds = array_filter($itemIds, function($id) {
                return $id > 0;
            });

            if (empty($itemIds)) {
                $this->error('物品ID列表为空');
                return;
            }

            // 限制批量查询数量
            if (count($itemIds) > 100) {
                $this->error('批量查询数量不能超过100个');
                return;
            }

            // 批量获取价格信息（简化版本的详情）
            $data = $this->itemsService->getBatchItemDetails($itemIds);

            // 只返回价格相关信息
            $priceData = [];
            foreach ($data as $itemId => $item) {
                $priceData[$itemId] = [
                    'id' => $item['id'],
                    'name' => $item['name'],
                    'current_price' => $item['current_price'],
                    'price_24h_ago' => $item['price_24h_ago']
                ];
            }

            PerformanceMonitor::endApi($monitor, $priceData, true);
            $this->success('获取成功', $priceData);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('批量获取物品价格失败: ' . $e->getMessage());
            $this->error('批量获取物品价格失败: ' . $e->getMessage());
        }
    }
}