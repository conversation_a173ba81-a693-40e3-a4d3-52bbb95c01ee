<?php

namespace app\api\controller;

use ba\Tree;
use Throwable;
use think\facade\Db;
use think\facade\Config;
use think\facade\Cache;
use app\common\controller\Frontend;
use app\common\library\token\TokenExpirationException;
use app\common\model\sjz\Announcements;
use app\common\service\CacheManager;
use app\common\service\PerformanceMonitor;
use app\common\service\IndexService;
use app\common\service\MapPasswordService;
use think\Response;

class Index extends Frontend
{
    protected array $noNeedLogin = ['index', 'getAnnouncements', 'getAnnouncementDetail', 'getMapPasswords', 'getPriceChangeItemsList', 'getPriceChangeItems1', 'getBulletKeyCardRankings'];

    private CacheManager $cacheManager;
    private IndexService $indexService;
    private MapPasswordService $mapPasswordService;

    public function initialize(): void
    {
        parent::initialize();
        $this->cacheManager = new CacheManager();
        $this->indexService = new IndexService();
        $this->mapPasswordService = new MapPasswordService();
    }

    /**
     * 前台和会员中心的初始化请求
     * @throws Throwable
     */
    public function index(): void
    {
        $menus = [];
        if ($this->auth->isLogin()) {
            $rules     = [];
            $userMenus = $this->auth->getMenus();

            // 首页加载的规则，验权，但过滤掉会员中心菜单
            foreach ($userMenus as $item) {
                if ($item['type'] == 'menu_dir') {
                    $menus[] = $item;
                } elseif ($item['type'] != 'menu') {
                    $rules[] = $item;
                }
            }
            $rules = array_values($rules);
        } else {
            // 若是从前台会员中心内发出的请求，要求必须登录，否则会员中心异常
            $requiredLogin = $this->request->get('requiredLogin/b', false);
            if ($requiredLogin) {

                // 触发可能的 token 过期异常
                try {
                    $token = get_auth_token(['ba', 'user', 'token']);
                    $this->auth->init($token);
                } catch (TokenExpirationException) {
                    $this->error(__('Token expiration'), [], 409);
                }

                $this->error(__('Please login first'), [
                    'type' => $this->auth::NEED_LOGIN
                ], $this->auth::LOGIN_RESPONSE_CODE);
            }

            $rules = Db::name('user_rule')
                ->where('status', '1')
                ->where('no_login_valid', 1)
                ->where('type', 'in', ['route', 'nav', 'button'])
                ->order('weigh', 'desc')
                ->select()
                ->toArray();
            $rules = Tree::instance()->assembleChild($rules);
        }

        $this->success('', [
            'site'             => [
                'siteName'     => get_sys_config('site_name'),
                'recordNumber' => get_sys_config('record_number'),
                'version'      => get_sys_config('version'),
                'cdnUrl'       => full_url(),
                'bannerImgHaozi' => get_sys_config('banner_img_haozi'),
                'upload'       => keys_to_camel_case(get_upload_config(), ['max_size', 'save_name', 'allowed_suffixes', 'allowed_mime_types']),
            ],
            'openMemberCenter' => Config::get('buildadmin.open_member_center'),
            'userInfo'         => $this->auth->getUserInfo(),
            'rules'            => $rules,
            'menus'            => $menus,
        ]);
    }

    /**
     * 获取公告列表
     * @param string $type 公告类型
     * @param string $search 搜索关键词
     * @param string $start_time 开始时间
     * @param string $end_time 结束时间
     * @param string $sort 排序方式 newest/oldest
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return Response
     */
    public function getAnnouncements()
    {
        $type = $this->request->param('type/a');  // 数组形式接收多个类型
        $search = $this->request->param('search/s', '');
        $startTime = $this->request->param('start_time/s', '');
        $endTime = $this->request->param('end_time/s', '');
        $sort = $this->request->param('sort/s', 'newest');
        $page = $this->request->param('page/d', 1);
        $pageSize = $this->request->param('pageSize/d', 20);

        // 构建缓存键，包含所有查询参数
        $cacheKey = 'announcements:list:' . md5(json_encode([
            'type' => $type,
            'search' => $search,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'sort' => $sort,
            'page' => $page,
            'pageSize' => $pageSize
        ]));

        // 使用 CacheManager 获取数据
        $data = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_DYNAMIC_DATA, function() use ($type, $search, $startTime, $endTime, $sort, $page, $pageSize) {
            return $this->fetchAnnouncementsData($type, $search, $startTime, $endTime, $sort, $page, $pageSize);
        });

        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => $data
        ]);

    }

    /**
     * 获取公告数据
     */
    private function fetchAnnouncementsData($type, $search, $startTime, $endTime, $sort, $page, $pageSize)
    {
        $model = new Announcements();
        $query = $model->where('status', '1'); // 只查询已发布的公告

        // 类型筛选
        if (!empty($type)) {
            $query = $query->whereIn('type', $type);
        }

        // 关键词搜索
        if ($search !== '') {
            $query = $query->where(function ($query) use ($search) {
                $query->where('title', 'like', "%{$search}%")
                    ->whereOr('brief', 'like', "%{$search}%")
                    ->whereOr('content', 'like', "%{$search}%");
            });
        }

        // 时间范围筛选
        if ($startTime && $endTime) {
            $query = $query->whereBetweenTime('publish_time', $startTime, $endTime);
        }

        // 排序
        $order = $sort === 'newest' ? 'publish_time DESC' : 'publish_time ASC';
        $query = $query->order($order);

        // 置顶公告优先
        $query = $query->order('is_top DESC');

        // 分页查询
        $total = $query->count();
        $list = $query->page($page, $pageSize)
            ->field('id, title, brief, type, is_top, publish_time, view_count,admin_name') // 只返回必要字段
            ->select();

        // 格式化数据
        $formattedList = [];
        foreach ($list as $item) {
            $itemArray = $item->toArray();
            $itemArray['time'] = strtotime($item->publish_time) * 1000; // 转换为毫秒级时间戳
            $formattedList[] = $itemArray;
        }

        return [
            'total' => $total,
            'list' => $formattedList
        ];
    }

    /**
     * 获取公告详情
     * @param int $id 公告ID
     * @return Response
     */
    public function getAnnouncementDetail()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 无论是否有缓存，都更新浏览次数
        $model = new Announcements();
        $model->where('id', $id)->inc('view_count')->update();

        // 构建缓存键
        $cacheKey = 'announcement:detail:' . $id;

        // 使用 CacheManager 获取数据
        $data = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_DYNAMIC_DATA, function() use ($id) {
            return $this->fetchAnnouncementDetail($id);
        });

        if (!$data) {
            return json(['code' => 0, 'msg' => '公告不存在']);
        }

        // 更新缓存中的浏览量（因为我们在缓存前已经更新了数据库）
        $data['view_count'] += 1;

        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => $data
        ]);
    }

    /**
     * 获取公告详情数据
     */
    private function fetchAnnouncementDetail($id)
    {
        $model = new Announcements();
        $detail = $model->where('id', $id)
            ->where('status', '1')
            ->find();

        if (!$detail) {
            return null;
        }

        // 转换为数组并格式化时间
        $detailArray = $detail->toArray();
        $detailArray['time'] = strtotime($detail->publish_time) * 1000;

        return $detailArray;
    }



    /**
     * 获取子弹和钥匙卡排行榜数据
     *
     * @return Response
     */
    public function getBulletKeyCardRankings()
    {
        $monitor = PerformanceMonitor::startApi('getBulletKeyCardRankings', request()->param());
        
        try {
            $limit = input('limit', 10, 'intval');
            
            $data = $this->indexService->getBulletKeyCardRankings($limit);
            
            PerformanceMonitor::endApi($monitor, $data, true);
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $data
            ]);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            return json([
                'code' => 0,
                'msg' => '获取排行榜数据失败: ' . $e->getMessage(),
                'data' => [
                    'bullet_rising' => [],
                    'bullet_falling' => [],
                    'keycard_rising' => [],
                    'keycard_falling' => []
                ]
            ]);
        }
    }


    /**
     * 清除公告相关缓存
     * 
     * @param int|null $id 公告ID，如果为null则清除所有公告缓存
     */
    protected function clearAnnouncementCache($id = null)
    {
        if ($id) {
            // 清除特定公告的缓存
            Cache::store('redis')->delete('announcement:detail:' . $id);
        }
        
        // 清除公告列表相关的所有缓存
        Cache::store('redis')->tag('announcements')->clear();
    }
    
    /**
     * 清除统计数据缓存
     */
    protected function clearStatsCache()
    {
        Cache::store('redis')->delete('stats:dashboard_summary');
        Cache::store('redis')->delete('stats:price_change_items');
        Cache::store('redis')->delete('stats:bullet_price_index');
        // 可以选择性地保留总物品数缓存，因为它变化不频繁
        // Cache::store('redis')->delete('stats:total_items');
    }

    /**
     * 获取地图密码数据
     * 
     * @return Response
     */
    public function getMapPasswords()
    {
        $monitor = PerformanceMonitor::startApi('getMapPasswords', request()->param());
        
        try {
            // 地图密码不使用缓存，直接从服务获取
            $data = $this->mapPasswordService->getMapPasswords();
            
            PerformanceMonitor::endApi($monitor, $data, false);
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $data
            ]);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            return json([
                'code' => 0,
                'msg' => '获取地图密码失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }




    /**
     * 获取价格涨跌幅最大的物品 (原始版本备份)
     *
     * @return Response
     */
    /**
     * 获取价格涨跌幅最大的物品列表 (统一使用ba_sjz_price_history表)
     * 返回前10个涨幅最大和前10个跌幅最大的物品
     * 对比当前时间段与24小时前时间段的价格
     *
     * @return Response
     */
    public function getPriceChangeItemsList()
    {
        $monitor = PerformanceMonitor::startApi('getPriceChangeItemsList', request()->param());
        
        try {
            $data = $this->indexService->getPriceChangeItemsList();
            
            PerformanceMonitor::endApi($monitor, $data, true);
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $data
            ]);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            return json([
                'code' => 0,
                'msg' => '获取价格变动列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }











    /**
     * 获取价格涨跌幅最大的物品 (统一使用ba_sjz_price_history表)
     */
    public function getPriceChangeItems1(): void
    {
        $monitor = PerformanceMonitor::startApi('getPriceChangeItems1', request()->param());

        try {
            $data = $this->indexService->getPriceChangeItems();

            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            $this->error('获取价格变动数据失败: ' . $e->getMessage());
        }
    }

}