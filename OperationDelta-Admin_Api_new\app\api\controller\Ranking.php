<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\controller\Frontend;
use app\common\service\PerformanceMonitor;
use app\common\service\RankingService;
use think\facade\Log;
use think\Request;

/**
 * 价格排行榜控制器 - Service架构版本
 * 
 * 重构记录:
 * 2025-08-03: Service架构重构
 *   - 业务逻辑迁移到RankingService
 *   - 控制器只负责参数处理和响应
 *   - 统一缓存管理通过Service层
 */
class Ranking extends Frontend
{
    protected array $noNeedLogin = ['getRankingList', 'getKeycardRanking', 'getBulletRanking', 'getBulletPackageRanking', 'getBulletPrices'];
    protected array $noNeedPermission = [];

    private RankingService $rankingService;

    public function __construct()
    {
        parent::__construct();
        $this->rankingService = new RankingService();
    }

    /**
     * 获取价格排行榜数据
     */
    public function getRankingList(Request $request): void
    {
        $monitor = PerformanceMonitor::startApi('getRankingList', $request->param());

        try {
            $params = [
                'type' => $request->param('type', 'highest_price'),
                'time_range' => $request->param('time_range', 'day'),
                'page' => max(1, intval($request->param('page', 1))),
                'page_size' => min(100, max(1, intval($request->param('page_size', 20)))),
                'grade' => $request->param('grade', ''),
                'item_type' => $request->param('item_type', ''),
                'min_price' => floatval($request->param('min_price', 0)),
                'max_price' => floatval($request->param('max_price', 0))
            ];

            $data = $this->rankingService->getRankingList($params);

            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取排行榜失败: ' . $e->getMessage());
            $this->error('获取排行榜失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取钥匙卡排行榜
     */
    public function getKeycardRanking(Request $request)
    {
        $monitor = PerformanceMonitor::startApi('getKeycardRanking', $request->param());
        
        try {
            $params = [
                'type' => $request->param('type', 'price_high_to_low'),
                'limit' => min(50, max(1, intval($request->param('limit', 20))))
            ];
            
            $data = $this->rankingService->getKeycardRanking($params);
            
            PerformanceMonitor::endApi($monitor, $data, true);
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'time' => time(),
                'data' => $data
            ]);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取钥匙卡排行榜失败: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '获取钥匙卡排行榜失败: ' . $e->getMessage(),
                'time' => time(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取子弹排行榜
     */
    public function getBulletRanking(Request $request)
    {
        $monitor = PerformanceMonitor::startApi('getBulletRanking', $request->param());
        
        try {
            $params = [
                'type' => $request->param('type', 'price_high_to_low'),
                'limit' => min(50, max(1, intval($request->param('limit', 20))))
            ];
            
            $data = $this->rankingService->getBulletRanking($params);
            
            PerformanceMonitor::endApi($monitor, $data, true);
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'time' => time(),
                'data' => $data
            ]);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取子弹排行榜失败: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '获取子弹排行榜失败: ' . $e->getMessage(),
                'time' => time(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取子弹包装排行榜
     */
    public function getBulletPackageRanking(Request $request)
    {
        $monitor = PerformanceMonitor::startApi('getBulletPackageRanking', $request->param());
        
        try {
            $params = [
                'type' => $request->param('type', 'efficiency'),
                'limit' => min(50, max(1, intval($request->param('limit', 20))))
            ];
            
            $data = $this->rankingService->getBulletPackageRanking($params);
            
            PerformanceMonitor::endApi($monitor, $data, true);
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'time' => time(),
                'data' => $data
            ]);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取子弹包装排行榜失败: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '获取子弹包装排行榜失败: ' . $e->getMessage(),
                'time' => time(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取子弹价格数据
     */
    public function getBulletPrices(Request $request)
    {
        $monitor = PerformanceMonitor::startApi('getBulletPrices', $request->param());
        
        try {
            $params = [
                'bullet_type' => $request->param('bullet_type', ''),
                'sort_by' => $request->param('sort_by', 'price'),
                'order' => $request->param('order', 'desc')
            ];
            
            $data = $this->rankingService->getBulletPrices($params);
            
            PerformanceMonitor::endApi($monitor, $data, true);
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'time' => time(),
                'data' => $data
            ]);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取子弹价格失败: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '获取子弹价格失败: ' . $e->getMessage(),
                'time' => time(),
                'data' => null
            ]);
        }
    }
}