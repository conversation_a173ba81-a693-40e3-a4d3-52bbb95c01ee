<?php

namespace app\api\controller;

use app\common\controller\Frontend;
use app\common\service\PerformanceMonitor;
use app\common\service\FeatureService;
use ba\Random;
use think\facade\Log;

/**
 * 功能投票控制器 - Service架构版本
 * 
 * 重构记录:
 * 2025-08-03: Service架构重构
 *   - 业务逻辑迁移到FeatureService
 *   - 控制器只负责参数处理和响应
 *   - 统一缓存管理通过Service层
 */
class Feature extends Frontend
{
    // 所有接口均不需要登录
    protected array $noNeedLogin = ['index', 'detail', 'submit', 'vote', 'getStatus'];
    
    private FeatureService $featureService;
    
    /**
     * 构造函数
     */
    public function initialize(): void
    {
        parent::initialize();
        
        $this->featureService = new FeatureService();
        
        // 优先取 X-Client-ID，没有再取 client-id
        $clientId = $this->request->header('X-Client-ID') ?: $this->request->header('client-id');
        if (!$clientId) {
            $clientId = Random::uuid();
            response()->header(['X-Client-ID' => $clientId]);
        }
        $this->request->clientId = $clientId;
    }
    
    /**
     * 获取功能请求列表
     */
    public function index(): void
    {
        $monitor = PerformanceMonitor::startApi('getFeatureList', $this->request->param());
        
        try {
            $params = [
                'status' => $this->request->param('status/a', []),
                'search' => $this->request->param('search/s', ''),
                'sort' => $this->request->param('sort/s', 'newest'),
                'page' => max(1, intval($this->request->param('page/d', 1))),
                'pageSize' => min(50, max(1, intval($this->request->param('pageSize/d', 20))))
            ];
            
            $data = $this->featureService->getFeatureList($params);
            
            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取功能请求列表失败: ' . $e->getMessage());
            $this->error('获取功能请求列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取功能请求详情
     */
    public function detail(): void
    {
        $monitor = PerformanceMonitor::startApi('getFeatureDetail', $this->request->param());

        try {
            $id = intval($this->request->param('id/d', 0));

            if ($id <= 0) {
                $this->error('功能请求ID无效');
                return;
            }

            $data = $this->featureService->getFeatureDetail($id);

            if (!$data) {
                $this->error('功能请求不存在');
                return;
            }

            PerformanceMonitor::endApi($monitor, $data, true);
            $this->success('获取成功', $data);

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取功能请求详情失败: ' . $e->getMessage());
            $this->error('获取功能请求详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 提交功能请求
     */
    public function submit(): void
    {
        $monitor = PerformanceMonitor::startApi('submitFeature', $this->request->param());

        try {
            $data = [
                'title' => trim($this->request->param('title/s', '')),
                'description' => trim($this->request->param('description/s', '')),
                'category' => $this->request->param('category/s', 'general'),
                'priority' => $this->request->param('priority/s', 'medium')
            ];

            $clientId = $this->request->clientId;

            $result = $this->featureService->submitFeature($data, $clientId);

            PerformanceMonitor::endApi($monitor, $result, false);

            if ($result['success']) {
                $this->success($result['message'], ['feature_id' => $result['feature_id']]);
            } else {
                $this->error($result['message']);
            }

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('提交功能请求失败: ' . $e->getMessage());
            $this->error('提交功能请求失败: ' . $e->getMessage());
        }
    }

    /**
     * 投票功能请求
     */
    public function vote(): void
    {
        $monitor = PerformanceMonitor::startApi('voteFeature', $this->request->param());

        try {
            $featureId = intval($this->request->param('feature_id/d', 0));

            if ($featureId <= 0) {
                $this->error('功能请求ID无效');
                return;
            }

            $clientId = $this->request->clientId;

            $result = $this->featureService->voteFeature($featureId, $clientId);

            PerformanceMonitor::endApi($monitor, $result, false);

            if ($result['success']) {
                $this->success($result['message'], ['vote_count' => $result['vote_count']]);
            } else {
                $this->error($result['message']);
            }

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('投票功能请求失败: ' . $e->getMessage());
            $this->error('投票功能请求失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取功能请求状态
     */
    public function getStatus(): void
    {
        $monitor = PerformanceMonitor::startApi('getFeatureStatus', $this->request->param());

        try {
            $featureId = intval($this->request->param('feature_id/d', 0));

            if ($featureId <= 0) {
                $this->error('功能请求ID无效');
                return;
            }

            $clientId = $this->request->clientId;

            $result = $this->featureService->getFeatureStatus($featureId, $clientId);

            PerformanceMonitor::endApi($monitor, $result, true);

            if ($result['exists']) {
                $this->success('获取成功', $result);
            } else {
                $this->error($result['message'] ?? '功能请求不存在');
            }

        } catch (\Throwable $e) {
            PerformanceMonitor::endApi($monitor);
            Log::error('获取功能请求状态失败: ' . $e->getMessage());
            $this->error('获取功能请求状态失败: ' . $e->getMessage());
        }
    }
}